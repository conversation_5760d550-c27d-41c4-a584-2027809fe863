using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using ModInstallerApp.Models;

namespace ModInstallerApp.Services
{
    /// <summary>
    /// Service for managing community ratings and reviews for mods and collections
    /// </summary>
    public class CommunityRatingService : IDisposable
    {
        private readonly EnhancedLogger _logger;
        private readonly string _ratingsPath;
        private readonly Dictionary<string, ModCollectionRating> _ratings = new();
        private readonly Dictionary<string, List<ModReview>> _reviews = new();
        private bool _disposed = false;

        public event EventHandler<ModReview>? ReviewAdded;
        public event EventHandler<ModReview>? ReviewUpdated;
        public event EventHandler<ModReview>? ReviewDeleted;
        public event EventHandler<string>? RatingUpdated; // ModId/CollectionId

        public CommunityRatingService(string appDataPath, EnhancedLogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _ratingsPath = Path.Combine(appDataPath, "CommunityRatings");
            Directory.CreateDirectory(_ratingsPath);
        }

        /// <summary>
        /// Initializes the service and loads existing ratings
        /// </summary>
        public async Task InitializeAsync()
        {
            try
            {
                _logger.LogInfo("Initializing Community Rating Service", "CommunityRating");
                await LoadRatingsAsync();
                _logger.LogInfo($"Community Rating Service initialized: {_ratings.Count} items rated", "CommunityRating");
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to initialize Community Rating Service", "CommunityRating", ex);
                throw;
            }
        }

        /// <summary>
        /// Adds or updates a review for a mod or collection
        /// </summary>
        public async Task<ModReview> AddReviewAsync(string itemId, string reviewerName, int rating, 
            string title = "", string comment = "", List<string>? tags = null)
        {
            try
            {
                if (rating < 1 || rating > 5)
                    throw new ArgumentException("Rating must be between 1 and 5", nameof(rating));

                var review = new ModReview
                {
                    ReviewerName = reviewerName,
                    Rating = rating,
                    Title = title,
                    Comment = comment,
                    Tags = tags ?? new List<string>(),
                    ReviewDate = DateTime.Now
                };

                // Add to reviews collection
                if (!_reviews.ContainsKey(itemId))
                {
                    _reviews[itemId] = new List<ModReview>();
                }

                // Check if reviewer already has a review for this item
                var existingReview = _reviews[itemId].FirstOrDefault(r => 
                    r.ReviewerName.Equals(reviewerName, StringComparison.OrdinalIgnoreCase));

                if (existingReview != null)
                {
                    // Update existing review
                    existingReview.Rating = rating;
                    existingReview.Title = title;
                    existingReview.Comment = comment;
                    existingReview.Tags = tags ?? new List<string>();
                    existingReview.ReviewDate = DateTime.Now;
                    
                    ReviewUpdated?.Invoke(this, existingReview);
                    _logger.LogInfo($"Updated review for {itemId} by {reviewerName}", "CommunityRating");
                }
                else
                {
                    // Add new review
                    _reviews[itemId].Add(review);
                    ReviewAdded?.Invoke(this, review);
                    _logger.LogInfo($"Added review for {itemId} by {reviewerName} (Rating: {rating})", "CommunityRating");
                }

                // Update overall rating
                await UpdateOverallRatingAsync(itemId);
                await SaveRatingsAsync();

                return existingReview ?? review;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to add review for {itemId}", "CommunityRating", ex);
                throw;
            }
        }

        /// <summary>
        /// Gets all reviews for a specific item
        /// </summary>
        public List<ModReview> GetReviews(string itemId)
        {
            return _reviews.ContainsKey(itemId) ? _reviews[itemId].ToList() : new List<ModReview>();
        }

        /// <summary>
        /// Gets the overall rating for an item
        /// </summary>
        public ModCollectionRating GetRating(string itemId)
        {
            return _ratings.ContainsKey(itemId) ? _ratings[itemId] : new ModCollectionRating();
        }

        /// <summary>
        /// Gets reviews filtered by criteria
        /// </summary>
        public List<ModReview> GetFilteredReviews(string itemId, int? minRating = null, 
            int? maxRating = null, List<string>? tags = null, DateTime? fromDate = null)
        {
            var reviews = GetReviews(itemId);

            if (minRating.HasValue)
                reviews = reviews.Where(r => r.Rating >= minRating.Value).ToList();

            if (maxRating.HasValue)
                reviews = reviews.Where(r => r.Rating <= maxRating.Value).ToList();

            if (tags != null && tags.Any())
                reviews = reviews.Where(r => r.Tags.Any(t => tags.Contains(t, StringComparer.OrdinalIgnoreCase))).ToList();

            if (fromDate.HasValue)
                reviews = reviews.Where(r => r.ReviewDate >= fromDate.Value).ToList();

            return reviews.OrderByDescending(r => r.ReviewDate).ToList();
        }

        /// <summary>
        /// Votes on a review's helpfulness
        /// </summary>
        public async Task<bool> VoteOnReviewAsync(string itemId, string reviewId, bool isHelpful)
        {
            try
            {
                if (!_reviews.ContainsKey(itemId))
                    return false;

                var review = _reviews[itemId].FirstOrDefault(r => r.Id == reviewId);
                if (review == null)
                    return false;

                review.TotalVotes++;
                if (isHelpful)
                    review.HelpfulVotes++;

                await SaveRatingsAsync();
                _logger.LogInfo($"Vote recorded for review {reviewId}: {(isHelpful ? "helpful" : "not helpful")}", "CommunityRating");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to vote on review {reviewId}", "CommunityRating", ex);
                return false;
            }
        }

        /// <summary>
        /// Deletes a review
        /// </summary>
        public async Task<bool> DeleteReviewAsync(string itemId, string reviewId)
        {
            try
            {
                if (!_reviews.ContainsKey(itemId))
                    return false;

                var review = _reviews[itemId].FirstOrDefault(r => r.Id == reviewId);
                if (review == null)
                    return false;

                _reviews[itemId].Remove(review);
                
                // Update overall rating
                await UpdateOverallRatingAsync(itemId);
                await SaveRatingsAsync();

                ReviewDeleted?.Invoke(this, review);
                _logger.LogInfo($"Deleted review {reviewId} for {itemId}", "CommunityRating");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Failed to delete review {reviewId}", "CommunityRating", ex);
                return false;
            }
        }

        /// <summary>
        /// Gets top-rated items
        /// </summary>
        public List<(string ItemId, ModCollectionRating Rating)> GetTopRatedItems(int count = 10, int minReviews = 3)
        {
            return _ratings
                .Where(kvp => kvp.Value.TotalRatings >= minReviews)
                .OrderByDescending(kvp => kvp.Value.AverageRating)
                .ThenByDescending(kvp => kvp.Value.TotalRatings)
                .Take(count)
                .Select(kvp => (kvp.Key, kvp.Value))
                .ToList();
        }

        /// <summary>
        /// Gets recent reviews across all items
        /// </summary>
        public List<(string ItemId, ModReview Review)> GetRecentReviews(int count = 20)
        {
            var allReviews = new List<(string ItemId, ModReview Review)>();
            
            foreach (var kvp in _reviews)
            {
                foreach (var review in kvp.Value)
                {
                    allReviews.Add((kvp.Key, review));
                }
            }

            return allReviews
                .OrderByDescending(r => r.Review.ReviewDate)
                .Take(count)
                .ToList();
        }

        /// <summary>
        /// Gets rating statistics
        /// </summary>
        public Dictionary<string, object> GetRatingStatistics()
        {
            var totalItems = _ratings.Count;
            var totalReviews = _reviews.Values.Sum(reviews => reviews.Count);
            var averageRating = _ratings.Values.Any() ? _ratings.Values.Average(r => r.AverageRating) : 0;
            
            var ratingDistribution = new Dictionary<int, int>();
            for (int i = 1; i <= 5; i++)
            {
                ratingDistribution[i] = _reviews.Values
                    .SelectMany(reviews => reviews)
                    .Count(r => r.Rating == i);
            }

            return new Dictionary<string, object>
            {
                ["TotalItems"] = totalItems,
                ["TotalReviews"] = totalReviews,
                ["AverageRating"] = Math.Round(averageRating, 2),
                ["RatingDistribution"] = ratingDistribution,
                ["MostActiveReviewers"] = GetMostActiveReviewers(10),
                ["RecentActivity"] = GetRecentReviews(5)
            };
        }

        /// <summary>
        /// Gets most active reviewers
        /// </summary>
        public List<(string ReviewerName, int ReviewCount)> GetMostActiveReviewers(int count = 10)
        {
            var reviewerCounts = new Dictionary<string, int>();

            foreach (var reviews in _reviews.Values)
            {
                foreach (var review in reviews)
                {
                    if (reviewerCounts.ContainsKey(review.ReviewerName))
                        reviewerCounts[review.ReviewerName]++;
                    else
                        reviewerCounts[review.ReviewerName] = 1;
                }
            }

            return reviewerCounts
                .OrderByDescending(kvp => kvp.Value)
                .Take(count)
                .Select(kvp => (kvp.Key, kvp.Value))
                .ToList();
        }

        private async Task UpdateOverallRatingAsync(string itemId)
        {
            if (!_reviews.ContainsKey(itemId) || !_reviews[itemId].Any())
            {
                _ratings.Remove(itemId);
                return;
            }

            var reviews = _reviews[itemId];
            var averageRating = reviews.Average(r => r.Rating);
            var totalRatings = reviews.Count;

            var ratingDistribution = new Dictionary<int, int>();
            for (int i = 1; i <= 5; i++)
            {
                ratingDistribution[i] = reviews.Count(r => r.Rating == i);
            }

            _ratings[itemId] = new ModCollectionRating
            {
                AverageRating = Math.Round(averageRating, 2),
                TotalRatings = totalRatings,
                Reviews = reviews.ToList(),
                RatingDistribution = ratingDistribution
            };

            RatingUpdated?.Invoke(this, itemId);
        }

        private async Task LoadRatingsAsync()
        {
            _ratings.Clear();
            _reviews.Clear();

            var ratingsFile = Path.Combine(_ratingsPath, "ratings.json");
            var reviewsFile = Path.Combine(_ratingsPath, "reviews.json");

            if (File.Exists(ratingsFile))
            {
                try
                {
                    var ratingsJson = await File.ReadAllTextAsync(ratingsFile);
                    var loadedRatings = JsonSerializer.Deserialize<Dictionary<string, ModCollectionRating>>(ratingsJson);
                    if (loadedRatings != null)
                    {
                        foreach (var kvp in loadedRatings)
                        {
                            _ratings[kvp.Key] = kvp.Value;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"Failed to load ratings: {ex.Message}", "CommunityRating");
                }
            }

            if (File.Exists(reviewsFile))
            {
                try
                {
                    var reviewsJson = await File.ReadAllTextAsync(reviewsFile);
                    var loadedReviews = JsonSerializer.Deserialize<Dictionary<string, List<ModReview>>>(reviewsJson);
                    if (loadedReviews != null)
                    {
                        foreach (var kvp in loadedReviews)
                        {
                            _reviews[kvp.Key] = kvp.Value;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"Failed to load reviews: {ex.Message}", "CommunityRating");
                }
            }
        }

        private async Task SaveRatingsAsync()
        {
            try
            {
                var ratingsFile = Path.Combine(_ratingsPath, "ratings.json");
                var reviewsFile = Path.Combine(_ratingsPath, "reviews.json");

                var ratingsJson = JsonSerializer.Serialize(_ratings, new JsonSerializerOptions { WriteIndented = true });
                var reviewsJson = JsonSerializer.Serialize(_reviews, new JsonSerializerOptions { WriteIndented = true });

                await File.WriteAllTextAsync(ratingsFile, ratingsJson);
                await File.WriteAllTextAsync(reviewsFile, reviewsJson);
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to save ratings and reviews", "CommunityRating", ex);
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
                _logger.LogInfo("Community Rating Service disposed", "CommunityRating");
            }
        }
    }
}
