using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using NUnit.Framework;
using NUnit.Framework.Api;
using NUnit.Framework.Interfaces;

namespace ModInstallerApp.Tests
{
    /// <summary>
    /// Comprehensive test runner for Phase 2.3 implementation
    /// Executes all test categories and generates detailed reports
    /// </summary>
    public class ComprehensiveTestRunner
    {
        private readonly List<TestResult> _testResults = new();
        private readonly Dictionary<string, List<TestResult>> _categoryResults = new();
        private readonly Stopwatch _totalExecutionTime = new();

        public static async Task<int> Main(string[] args)
        {
            var runner = new ComprehensiveTestRunner();
            return await runner.RunAllTestsAsync(args);
        }

        public async Task<int> RunAllTestsAsync(string[] args)
        {
            Console.WriteLine("=== Palworld Mod Installer - Phase 2.3 Comprehensive Test Suite ===");
            Console.WriteLine("Testing & Quality Assurance Implementation");
            Console.WriteLine($"Started at: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine();

            _totalExecutionTime.Start();

            try
            {
                // Parse command line arguments
                var options = ParseArguments(args);
                
                // Run test categories based on options
                var success = true;
                
                if (options.RunUnit)
                    success &= await RunTestCategoryAsync(TestCategories.Unit);
                
                if (options.RunIntegration)
                    success &= await RunTestCategoryAsync(TestCategories.Integration);
                
                if (options.RunSecurity)
                    success &= await RunTestCategoryAsync(TestCategories.Security);
                
                if (options.RunPerformance)
                    success &= await RunTestCategoryAsync(TestCategories.Performance);
                
                if (options.RunUI)
                    success &= await RunTestCategoryAsync(TestCategories.UI);
                
                if (options.RunErrorRecovery)
                    success &= await RunTestCategoryAsync(TestCategories.ErrorRecovery);

                // If no specific categories selected, run all
                if (!options.HasSpecificCategories)
                {
                    success &= await RunAllTestCategoriesAsync();
                }

                _totalExecutionTime.Stop();

                // Generate reports
                await GenerateTestReportAsync(options);
                
                // Display summary
                DisplayTestSummary();

                return success ? 0 : 1;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fatal error during test execution: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return -1;
            }
        }

        private async Task<bool> RunAllTestCategoriesAsync()
        {
            Console.WriteLine("Running all test categories...");
            Console.WriteLine();

            var success = true;
            
            success &= await RunTestCategoryAsync(TestCategories.Unit);
            success &= await RunTestCategoryAsync(TestCategories.Integration);
            success &= await RunTestCategoryAsync(TestCategories.Security);
            success &= await RunTestCategoryAsync(TestCategories.Performance);
            success &= await RunTestCategoryAsync(TestCategories.UI);
            success &= await RunTestCategoryAsync(TestCategories.ErrorRecovery);

            return success;
        }

        private async Task<bool> RunTestCategoryAsync(string category)
        {
            Console.WriteLine($"=== Running {category} Tests ===");
            var categoryStopwatch = Stopwatch.StartNew();

            try
            {
                // Run actual NUnit tests for the category
                var testRunner = new NUnitTestAssemblyRunner(new DefaultTestAssemblyBuilder());
                var assembly = Assembly.GetExecutingAssembly();

                var settings = new Dictionary<string, object>
                {
                    ["WorkDirectory"] = TestConfiguration.TestDataRoot
                };

                var loadResult = testRunner.Load(assembly, settings);
                if (loadResult.HasChildren)
                {
                    // Filter tests by category
                    var filter = new CategoryFilter(category);
                    var runResult = testRunner.Run(TestListener.NULL, filter);

                    categoryStopwatch.Stop();

                    // Process results
                    var categoryResults = ProcessTestResult(runResult, category);
                    _categoryResults[category] = categoryResults;

                    var passed = categoryResults.Count(r => r.Status == TestStatus.Passed);
                    var failed = categoryResults.Count(r => r.Status == TestStatus.Failed);
                    var skipped = categoryResults.Count(r => r.Status == TestStatus.Skipped);

                    Console.WriteLine($"{category} Tests Summary:");
                    Console.WriteLine($"  Passed: {passed}");
                    Console.WriteLine($"  Failed: {failed}");
                    Console.WriteLine($"  Skipped: {skipped}");
                    Console.WriteLine($"  Duration: {categoryStopwatch.Elapsed.TotalSeconds:F2}s");
                    Console.WriteLine();

                    return failed == 0;
                }
                else
                {
                    Console.WriteLine($"No tests found for category: {category}");
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error running {category} tests: {ex.Message}");
                return false;
            }
        }

        private List<TestResult> ProcessTestResult(ITestResult result, string category)
        {
            var results = new List<TestResult>();
            
            if (result.HasChildren)
            {
                foreach (var child in result.Children)
                {
                    results.AddRange(ProcessTestResult(child, category));
                }
            }
            else
            {
                var testResult = new TestResult
                {
                    Name = result.Name,
                    FullName = result.FullName,
                    Category = category,
                    Status = ConvertTestStatus(result.ResultState.Status),
                    Duration = result.Duration,
                    Message = result.Message,
                    StackTrace = result.StackTrace
                };
                
                results.Add(testResult);
                _testResults.Add(testResult);
            }
            
            return results;
        }

        private TestStatus ConvertTestStatus(NUnit.Framework.Interfaces.TestStatus nunitStatus)
        {
            return nunitStatus switch
            {
                NUnit.Framework.Interfaces.TestStatus.Passed => TestStatus.Passed,
                NUnit.Framework.Interfaces.TestStatus.Failed => TestStatus.Failed,
                NUnit.Framework.Interfaces.TestStatus.Skipped => TestStatus.Skipped,
                NUnit.Framework.Interfaces.TestStatus.Inconclusive => TestStatus.Inconclusive,
                _ => TestStatus.Inconclusive
            };
        }

        private async Task GenerateTestReportAsync(TestOptions options)
        {
            if (!options.GenerateReport)
                return;

            var reportPath = Path.Combine(TestConfiguration.TestDataRoot, "TestReport.html");
            
            var html = GenerateHtmlReport();
            await File.WriteAllTextAsync(reportPath, html);
            
            Console.WriteLine($"Test report generated: {reportPath}");
            
            if (options.OpenReport)
            {
                try
                {
                    Process.Start(new ProcessStartInfo(reportPath) { UseShellExecute = true });
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Could not open report: {ex.Message}");
                }
            }
        }

        private string GenerateHtmlReport()
        {
            var totalTests = _testResults.Count;
            var passed = _testResults.Count(r => r.Status == TestStatus.Passed);
            var failed = _testResults.Count(r => r.Status == TestStatus.Failed);
            var skipped = _testResults.Count(r => r.Status == TestStatus.Skipped);
            var successRate = totalTests > 0 ? (passed * 100.0 / totalTests) : 0;

            return $@"
<!DOCTYPE html>
<html>
<head>
    <title>Palworld Mod Installer - Test Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .summary {{ display: flex; gap: 20px; margin: 20px 0; }}
        .metric {{ background-color: #e8f4f8; padding: 15px; border-radius: 5px; text-align: center; }}
        .passed {{ background-color: #d4edda; }}
        .failed {{ background-color: #f8d7da; }}
        .skipped {{ background-color: #fff3cd; }}
        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .status-passed {{ color: green; font-weight: bold; }}
        .status-failed {{ color: red; font-weight: bold; }}
        .status-skipped {{ color: orange; font-weight: bold; }}
    </style>
</head>
<body>
    <div class='header'>
        <h1>Palworld Mod Installer - Phase 2.3 Test Report</h1>
        <p>Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}</p>
        <p>Total Execution Time: {_totalExecutionTime.Elapsed.TotalSeconds:F2} seconds</p>
    </div>
    
    <div class='summary'>
        <div class='metric'>
            <h3>Total Tests</h3>
            <div style='font-size: 24px; font-weight: bold;'>{totalTests}</div>
        </div>
        <div class='metric passed'>
            <h3>Passed</h3>
            <div style='font-size: 24px; font-weight: bold;'>{passed}</div>
        </div>
        <div class='metric failed'>
            <h3>Failed</h3>
            <div style='font-size: 24px; font-weight: bold;'>{failed}</div>
        </div>
        <div class='metric skipped'>
            <h3>Skipped</h3>
            <div style='font-size: 24px; font-weight: bold;'>{skipped}</div>
        </div>
        <div class='metric'>
            <h3>Success Rate</h3>
            <div style='font-size: 24px; font-weight: bold;'>{successRate:F1}%</div>
        </div>
    </div>
    
    <h2>Test Results by Category</h2>
    {GenerateCategoryTables()}
    
    <h2>All Test Results</h2>
    {GenerateTestTable(_testResults)}
</body>
</html>";
        }

        private string GenerateCategoryTables()
        {
            var html = "";
            foreach (var category in _categoryResults)
            {
                html += $"<h3>{category.Key} Tests</h3>";
                html += GenerateTestTable(category.Value);
            }
            return html;
        }

        private string GenerateTestTable(List<TestResult> results)
        {
            var html = @"
<table>
    <tr>
        <th>Test Name</th>
        <th>Status</th>
        <th>Duration (ms)</th>
        <th>Message</th>
    </tr>";

            foreach (var result in results)
            {
                var statusClass = result.Status.ToString().ToLower();
                html += $@"
    <tr>
        <td>{result.Name}</td>
        <td class='status-{statusClass}'>{result.Status}</td>
        <td>{result.Duration * 1000:F0}</td>
        <td>{result.Message ?? ""}</td>
    </tr>";
            }

            html += "</table>";
            return html;
        }

        private void DisplayTestSummary()
        {
            Console.WriteLine("=== FINAL TEST SUMMARY ===");
            
            var totalTests = _testResults.Count;
            var passed = _testResults.Count(r => r.Status == TestStatus.Passed);
            var failed = _testResults.Count(r => r.Status == TestStatus.Failed);
            var skipped = _testResults.Count(r => r.Status == TestStatus.Skipped);
            
            Console.WriteLine($"Total Tests: {totalTests}");
            Console.WriteLine($"Passed: {passed}");
            Console.WriteLine($"Failed: {failed}");
            Console.WriteLine($"Skipped: {skipped}");
            Console.WriteLine($"Success Rate: {(totalTests > 0 ? passed * 100.0 / totalTests : 0):F1}%");
            Console.WriteLine($"Total Execution Time: {_totalExecutionTime.Elapsed.TotalSeconds:F2} seconds");
            
            if (failed == 0)
            {
                Console.WriteLine();
                Console.WriteLine("✅ ALL TESTS PASSED!");
                Console.WriteLine("Phase 2.3 Testing & Quality Assurance implementation is working correctly!");
            }
            else
            {
                Console.WriteLine();
                Console.WriteLine("❌ SOME TESTS FAILED");
                Console.WriteLine("Please review the failed tests and fix the issues.");
            }
        }

        private TestOptions ParseArguments(string[] args)
        {
            var options = new TestOptions();
            
            foreach (var arg in args)
            {
                switch (arg.ToLower())
                {
                    case "--unit":
                        options.RunUnit = true;
                        break;
                    case "--integration":
                        options.RunIntegration = true;
                        break;
                    case "--security":
                        options.RunSecurity = true;
                        break;
                    case "--performance":
                        options.RunPerformance = true;
                        break;
                    case "--ui":
                        options.RunUI = true;
                        break;
                    case "--error-recovery":
                        options.RunErrorRecovery = true;
                        break;
                    case "--report":
                        options.GenerateReport = true;
                        break;
                    case "--open-report":
                        options.GenerateReport = true;
                        options.OpenReport = true;
                        break;
                }
            }
            
            return options;
        }
    }

    public class TestOptions
    {
        public bool RunUnit { get; set; }
        public bool RunIntegration { get; set; }
        public bool RunSecurity { get; set; }
        public bool RunPerformance { get; set; }
        public bool RunUI { get; set; }
        public bool RunErrorRecovery { get; set; }
        public bool GenerateReport { get; set; } = true;
        public bool OpenReport { get; set; }
        
        public bool HasSpecificCategories => RunUnit || RunIntegration || RunSecurity || RunPerformance || RunUI || RunErrorRecovery;
    }

    public class TestResult
    {
        public string Name { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public TestStatus Status { get; set; }
        public double Duration { get; set; }
        public string? Message { get; set; }
        public string? StackTrace { get; set; }
    }

    public enum TestStatus
    {
        Passed,
        Failed,
        Skipped,
        Inconclusive
    }

    /// <summary>
    /// Category filter for NUnit tests
    /// </summary>
    public class CategoryFilter : ITestFilter
    {
        private readonly string _category;

        public CategoryFilter(string category)
        {
            _category = category;
        }

        public bool Pass(ITest test)
        {
            return test.Properties.ContainsKey("Category") &&
                   test.Properties["Category"].Contains(_category);
        }

        public bool Match(ITest test)
        {
            return Pass(test);
        }

        public bool IsExplicitMatch(ITest test)
        {
            return false; // Not an explicit match filter
        }

        public TNode ToXml(bool recursive)
        {
            return new TNode("cat", _category);
        }

        public TNode AddToXml(TNode parentNode, bool recursive)
        {
            return parentNode.AddElement("cat", _category);
        }
    }
}
