using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security;
using System.Threading;
using System.Threading.Tasks;
using NUnit.Framework;
using FluentAssertions;
using Moq;
using ModInstallerApp.Services;
using ModInstallerApp.Models;
using ModInstallerApp.Tests.Mocks;

namespace ModInstallerApp.Tests.Services
{
    /// <summary>
    /// Comprehensive unit tests for EnhancedInstallationEngine service
    /// </summary>
    [TestFixture]
    public class EnhancedInstallationEngineTests : TestBase
    {
        private EnhancedInstallationEngine? _engine;
        private Mock<UE4SSDetector>? _mockDetector;

        [SetUp]
        public override async Task SetUp()
        {
            await base.SetUp();
            
            _mockDetector = MockServices.CreateMockUE4SSDetector();
            _engine = new EnhancedInstallationEngine(TestPalworldRoot, _mockDetector.Object, TestCacheManager!, TestLogger!);
        }

        [TearDown]
        public override async Task TearDown()
        {
            _engine?.Dispose();
            await base.TearDown();
        }

        [Test]
        public async Task InstallModAsync_WithValidArchive_InstallsSuccessfully()
        {
            // Arrange
            var archivePath = await CreateTestModArchiveAsync("TestMod", ModStructureType.UE4SS);
            var progressReports = new List<InstallationProgress>();
            var progress = new Progress<InstallationProgress>(p => progressReports.Add(p));
            
            var options = new InstallationOptions();

            // Act
            var result = await _engine!.InstallModAsync(archivePath, options, progress, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Operation.Should().NotBeNull();
            progressReports.Should().NotBeEmpty();
            progressReports.Last().FileProgressPercent.Should().Be(100);
        }

        [Test]
        public async Task InstallModAsync_WithCancellation_CancelsGracefully()
        {
            // Arrange
            var archivePath = await CreateTestModArchiveAsync("TestMod", ModStructureType.UE4SS);
            var cts = new CancellationTokenSource();
            var progress = new Progress<InstallationProgress>();
            
            // Cancel immediately
            cts.Cancel();
            
            // Act & Assert
            await Assert.ThrowsAsync<OperationCanceledException>(
                async () => await _engine!.InstallModAsync(archivePath, new InstallationOptions(), progress, cts.Token));
        }

        [Test]
        public async Task InstallModAsync_WithInvalidArchive_ThrowsException()
        {
            // Arrange
            var invalidArchivePath = Path.Combine(TestModsDirectory, "invalid.zip");
            await File.WriteAllTextAsync(invalidArchivePath, "not a valid archive");
            var progress = new Progress<InstallationProgress>();
            
            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(
                async () => await _engine!.InstallModAsync(invalidArchivePath, new InstallationOptions(), progress, CancellationToken.None));
        }

        [Test]
        public async Task InstallModAsync_WithNonExistentArchive_ThrowsFileNotFoundException()
        {
            // Arrange
            var nonExistentPath = Path.Combine(TestModsDirectory, "nonexistent.zip");
            var progress = new Progress<InstallationProgress>();
            
            // Act & Assert
            var options = new InstallationOptions();
            await Assert.ThrowsAsync<FileNotFoundException>(
                () => _engine!.InstallModAsync(nonExistentPath, options, progress, CancellationToken.None));
        }

        [Test]
        public async Task RollbackInstallationAsync_WithValidInstallation_RollsBackSuccessfully()
        {
            // Arrange
            var archivePath = await CreateTestModArchiveAsync("TestMod", ModStructureType.UE4SS);
            var options = new InstallationOptions();
            var installResult = await _engine!.InstallModAsync(archivePath, options, null, CancellationToken.None);
            var progress = new Progress<InstallationProgress>();

            // Act
            var rollbackResult = await _engine!.RollbackInstallationAsync(installResult.Operation!.Id);

            // Assert
            rollbackResult.Success.Should().BeTrue();
            
            // Verify installation is no longer in the list
            var installations = await _engine!.GetAllInstallationsAsync();
            installations.Should().NotContain(i => i.Id == installResult.Operation!.Id);
        }

        [Test]
        public async Task RollbackInstallationAsync_WithInvalidId_ReturnsFalse()
        {
            // Arrange
            var invalidId = Guid.NewGuid().ToString();
            var progress = new Progress<InstallationProgress>();
            
            // Act
            var result = await _engine!.RollbackInstallationAsync(invalidId);

            // Assert
            result.Success.Should().BeFalse();
        }

        [Test]
        public async Task GetAllInstallationsAsync_ReturnsAllInstallations()
        {
            // Arrange
            var archive1 = await CreateTestModArchiveAsync("TestMod1", ModStructureType.UE4SS);
            var archive2 = await CreateTestModArchiveAsync("TestMod2", ModStructureType.PalSchema);
            
            var options = new InstallationOptions();
            await _engine!.InstallModAsync(archive1, options, null, CancellationToken.None);
            await _engine!.InstallModAsync(archive2, options, null, CancellationToken.None);
            
            // Act
            var installations = await _engine!.GetAllInstallationsAsync();
            
            // Assert
            installations.Should().HaveCount(2);
            installations.Should().Contain(i => i.ModName == "TestMod1");
            installations.Should().Contain(i => i.ModName == "TestMod2");
        }

        [Test]
        public async Task InstallModAsync_WithDuplicateMod_HandlesConflict()
        {
            // Arrange
            var archivePath = await CreateTestModArchiveAsync("TestMod", ModStructureType.UE4SS);
            
            // Install first time
            var options = new InstallationOptions();
            await _engine!.InstallModAsync(archivePath, options, null, CancellationToken.None);

            // Act - Install same mod again
            var result = await _engine!.InstallModAsync(archivePath, options, null, CancellationToken.None);
            
            // Assert
            result.Should().NotBeNull();
            // Should handle duplicate installation gracefully
        }

        [Test]
        public async Task InstallModAsync_WithProgressReporting_ReportsProgress()
        {
            // Arrange
            var archivePath = await CreateTestModArchiveAsync("TestMod", ModStructureType.UE4SS);
            var progressReports = new List<InstallationProgress>();
            var progress = new Progress<InstallationProgress>(p => progressReports.Add(p));
            
            // Act
            var options = new InstallationOptions();
            await _engine!.InstallModAsync(archivePath, options, progress, CancellationToken.None);

            // Assert
            progressReports.Should().NotBeEmpty();
            progressReports.Should().Contain(p => p.FileProgressPercent == 0);
            progressReports.Should().Contain(p => p.FileProgressPercent == 100);

            // Verify progress is monotonically increasing
            for (int i = 1; i < progressReports.Count; i++)
            {
                progressReports[i].FileProgressPercent.Should().BeGreaterOrEqualTo(progressReports[i - 1].FileProgressPercent);
            }
        }

        [Test]
        public async Task InstallModAsync_WithPAKMod_InstallsToCorrectLocation()
        {
            // Arrange
            var archivePath = await CreateTestModArchiveAsync("TestPAKMod", ModStructureType.PAK);
            var progress = new Progress<InstallationProgress>();
            
            // Act
            var options = new InstallationOptions();
            var result = await _engine!.InstallModAsync(archivePath, options, progress, CancellationToken.None);
            
            // Assert
            result.Should().NotBeNull();
            result.Operation!.DetectedStructure!.ModName.Should().Be("TestPAKMod");
            
            // Verify PAK file was installed to correct location
            var pakPath = Path.Combine(TestPalworldRoot, "Pal", "Content", "Paks", "~mods", "TestPAKMod.pak");
            File.Exists(pakPath).Should().BeTrue();
        }

        [Test]
        public async Task InstallModAsync_WithPalSchemaMod_InstallsToCorrectLocation()
        {
            // Arrange
            var archivePath = await CreateTestModArchiveAsync("TestPalSchemaMod", ModStructureType.PalSchema);
            var progress = new Progress<InstallationProgress>();
            
            // Act
            var options = new InstallationOptions();
            var result = await _engine!.InstallModAsync(archivePath, options, progress, CancellationToken.None);
            
            // Assert
            result.Should().NotBeNull();
            result.Operation!.DetectedStructure!.ModName.Should().Be("TestPalSchemaMod");
            
            // Verify PalSchema mod was installed to correct location
            var palSchemaModPath = Path.Combine(TestPalworldRoot, "Pal", "Binaries", "Win64", "ue4ss", "Mods", "palschema", "mods", "TestPalSchemaMod");
            Directory.Exists(palSchemaModPath).Should().BeTrue();
        }

        [Test]
        public async Task RollbackInstallationAsync_WithProgressReporting_ReportsProgress()
        {
            // Arrange
            var archivePath = await CreateTestModArchiveAsync("TestMod", ModStructureType.UE4SS);
            var options = new InstallationOptions();
            var installResult = await _engine!.InstallModAsync(archivePath, options, null, CancellationToken.None);

            var progressReports = new List<InstallationProgress>();
            var progress = new Progress<InstallationProgress>(p => progressReports.Add(p));

            // Act
            await _engine!.RollbackInstallationAsync(installResult.Operation!.Id);

            // Assert
            progressReports.Should().NotBeEmpty();
            progressReports.Last().FileProgressPercent.Should().Be(100);
        }

        [Test]
        public async Task InstallModAsync_WithSecurityValidation_RejectsUnsafeArchives()
        {
            // Arrange
            var maliciousArchivePath = Path.Combine(TestModsDirectory, "malicious.zip");
            // Create a mock malicious archive that would fail security validation
            await File.WriteAllTextAsync(maliciousArchivePath, "malicious content");
            var progress = new Progress<InstallationProgress>();
            
            // Act & Assert
            var options = new InstallationOptions();
            await Assert.ThrowsAsync<SecurityException>(
                () => _engine!.InstallModAsync(maliciousArchivePath, options, progress, CancellationToken.None));
        }

        [Test]
        public void Constructor_WithInvalidParameters_ThrowsException()
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => new EnhancedInstallationEngine("", _mockDetector!.Object, TestCacheManager!, TestLogger!));
            Assert.Throws<ArgumentNullException>(() => new EnhancedInstallationEngine(TestPalworldRoot, null!, TestCacheManager!, TestLogger!));
            Assert.Throws<ArgumentNullException>(() => new EnhancedInstallationEngine(TestPalworldRoot, _mockDetector!.Object, null!, TestLogger!));
            Assert.Throws<ArgumentNullException>(() => new EnhancedInstallationEngine(TestPalworldRoot, _mockDetector!.Object, TestCacheManager!, null!));
        }

        [Test]
        public void Dispose_DisposesResourcesProperly()
        {
            // Arrange
            var engine = new EnhancedInstallationEngine(TestPalworldRoot, _mockDetector!.Object, TestCacheManager!, TestLogger!);
            
            // Act
            engine.Dispose();
            
            // Assert
            Assert.ThrowsAsync<ObjectDisposedException>(async () => await engine.GetAllInstallationsAsync());
        }
    }
}
