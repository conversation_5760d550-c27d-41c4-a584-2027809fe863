using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using NUnit.Framework;
using FluentAssertions;
using ModInstallerApp.Services;
using ModInstallerApp.Models;

namespace ModInstallerApp.Tests.Services
{
    /// <summary>
    /// Comprehensive unit tests for AppDataManager service
    /// </summary>
    [TestFixture]
    [Category(TestCategories.Unit)]
    public class AppDataManagerTests : TestBase
    {
        private AppDataManager? _appDataManager;
        private string _testAppDataPath = string.Empty;

        [SetUp]
        public override async Task SetUp()
        {
            await base.SetUp();

            _testAppDataPath = Path.Combine(TestDataDirectory, "test-appdata");
            Directory.CreateDirectory(_testAppDataPath);
            _appDataManager = AppDataManager.Instance;
        }

        [TearDown]
        public override async Task TearDown()
        {
            _appDataManager?.Dispose();
            await base.TearDown();
        }

        [Test]
        public void Settings_InitialCall_ReturnsDefaultSettings()
        {
            // Act
            var settings = _appDataManager!.Settings;

            // Assert
            settings.Should().NotBeNull();
            settings.RecentInstallations.Should().BeEmpty();
        }

        [Test]
        public void SaveSettings_PersistsSettings()
        {
            // Arrange
            _appDataManager!.Settings.LastPalworldPath = "C:\\Test\\Palworld1";

            // Act
            _appDataManager.SaveSettings();

            // Assert
            var retrievedPath = _appDataManager.Settings.LastPalworldPath;
            retrievedPath.Should().Be("C:\\Test\\Palworld1");
        }

        [Test]
        public void AddRecentInstallation_WithNewPath_AddsToRecentList()
        {
            // Arrange
            const string newPath = "C:\\Test\\NewPalworld\\Palworld.exe";

            // Act
            _appDataManager!.AddRecentInstallation(newPath);

            // Assert
            var settings = _appDataManager.Settings;
            settings.RecentInstallations.Should().Contain(i => i.Path == newPath);
        }

        [Test]
        public void AddRecentInstallation_WithExistingPath_MovesToTop()
        {
            // Arrange
            const string existingPath = "C:\\Test\\ExistingPalworld";
            const string newPath = "C:\\Test\\NewPalworld";
            
            _appDataManager!.AddRecentInstallation(existingPath);
            _appDataManager.AddRecentInstallation(newPath);

            // Act
            _appDataManager.AddRecentInstallation(existingPath);

            // Assert
            var settings = _appDataManager.Settings;
            settings.RecentInstallations.First().Path.Should().Be(existingPath);
            settings.RecentInstallations.Should().HaveCount(2);
        }

        [Test]
        public void AddRecentInstallation_ExceedsMaxCount_RemovesOldest()
        {
            // Arrange
            const int maxRecentInstallations = 10; // Assuming this is the limit
            
            // Add more than the maximum
            for (int i = 0; i < maxRecentInstallations + 2; i++)
            {
                _appDataManager!.AddRecentInstallation($"C:\\Test\\Palworld{i}");
            }

            // Assert
            var settings = _appDataManager!.Settings;
            settings.RecentInstallations.Should().HaveCount(maxRecentInstallations);
            settings.RecentInstallations.Should().NotContain(i => i.Path == "C:\\Test\\Palworld0");
            settings.RecentInstallations.Should().NotContain(i => i.Path == "C:\\Test\\Palworld1");
        }

        [Test]
        public void AutoDetectPalworldInstallations_FindsValidInstallations()
        {
            // Arrange
            var testInstallPath = Path.Combine(TestDataDirectory, "TestPalworld");
            var binariesPath = Path.Combine(testInstallPath, "Pal", "Binaries", "Win64");
            Directory.CreateDirectory(binariesPath);
            File.WriteAllText(Path.Combine(binariesPath, "Palworld-Win64-Shipping.exe"), "test");

            // Act
            var detectedInstallations = _appDataManager!.AutoDetectPalworldInstallations();

            // Assert
            // Note: This test may not find the test installation since AutoDetect typically
            // searches standard Steam/Epic locations. The test verifies the method doesn't crash.
            detectedInstallations.Should().NotBeNull();
        }

        [Test]
        public void RemoveRecentInstallation_WithExistingPath_RemovesFromList()
        {
            // Arrange
            const string pathToRemove = "C:\\Test\\ToRemove";
            const string pathToKeep = "C:\\Test\\ToKeep";
            
            _appDataManager!.AddRecentInstallation(pathToRemove);
            _appDataManager.AddRecentInstallation(pathToKeep);

            // Act
            _appDataManager.RemoveRecentInstallation(pathToRemove);

            // Assert
            var settings = _appDataManager.Settings;
            settings.RecentInstallations.Should().NotContain(i => i.Path == pathToRemove);
            settings.RecentInstallations.Should().Contain(i => i.Path == pathToKeep);
        }

        [Test]
        public void SaveSettings_CallsSuccessfully()
        {
            // Act & Assert - SaveSettings() no longer takes parameters
            _appDataManager!.SaveSettings();
            // Test passes if no exception is thrown
        }

        [Test]
        public void AddRecentInstallation_WithNullPath_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => _appDataManager!.AddRecentInstallation(null!));
        }

        [Test]
        public void AddRecentInstallation_WithEmptyPath_ThrowsArgumentException()
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => _appDataManager!.AddRecentInstallation(""));
            Assert.Throws<ArgumentException>(() => _appDataManager!.AddRecentInstallation("   "));
        }

        [Test]
        public void AppDataManager_IsSingleton()
        {
            // Act
            var instance1 = AppDataManager.Instance;
            var instance2 = AppDataManager.Instance;

            // Assert
            instance1.Should().BeSameAs(instance2);
        }

        [Test]
        public void SettingsPersistence_AcrossInstances_MaintainsData()
        {
            // Arrange
            _appDataManager!.AddRecentInstallation("C:\\Test\\Persistent");
            _appDataManager.Settings.LastPalworldPath = "C:\\Test\\Persistent";

            // Act
            _appDataManager.SaveSettings();

            // Assert
            var retrievedSettings = _appDataManager.Settings;
            retrievedSettings.RecentInstallations.Should().Contain(i => i.Path == "C:\\Test\\Persistent");
            retrievedSettings.LastPalworldPath.Should().Be("C:\\Test\\Persistent");
        }

        [Test]
        public void Dispose_DisposesResourcesProperly()
        {
            // Arrange
            _appDataManager!.AddRecentInstallation("C:\\Test\\Dispose");

            // Act
            _appDataManager.Dispose();

            // Assert
            // Test passes if no exception is thrown during disposal
            // Note: Singleton pattern means we can't test ObjectDisposedException easily
        }
    }
}
