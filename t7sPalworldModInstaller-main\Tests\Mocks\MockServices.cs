using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Moq;
using ModInstallerApp.Models;
using ModInstallerApp.Services;

namespace ModInstallerApp.Tests.Mocks
{
    /// <summary>
    /// Mock implementations and factory methods for testing
    /// </summary>
    public static class MockServices
    {
        /// <summary>
        /// Creates a mock CacheManager with configurable behavior
        /// </summary>
        public static Mock<CacheManager> CreateMockCacheManager()
        {
            var mock = new Mock<CacheManager>("test-cache-path");
            var cache = new Dictionary<string, object>();
            
            mock.Setup(x => x.Get<It.IsAnyType>(It.IsAny<string>()))
                .Returns<string>(key => cache.ContainsKey(key) ? (It.IsAnyType)cache[key] : default(It.IsAnyType));
            
            mock.Setup(x => x.Set(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<TimeSpan?>()))
                .Callback<string, object, TimeSpan?>((key, value, expiry) => cache[key] = value);
            
            mock.Setup(x => x.InvalidateCache(It.IsAny<string>()))
                .Callback<string>(key => cache.Remove(key, out _));
            
            mock.Setup(x => x.ClearAll())
                .Callback(() => cache.Clear());
            
            return mock;
        }

        /// <summary>
        /// Creates a mock EnhancedLogger with configurable behavior
        /// </summary>
        public static Mock<EnhancedLogger> CreateMockLogger()
        {
            var mock = new Mock<EnhancedLogger>(Mock.Of<Action<string>>());
            var logEntries = new List<LogEntry>();
            
            mock.Setup(x => x.LogInfo(It.IsAny<string>(), It.IsAny<string>()))
                .Callback((string message, string category) =>
                    logEntries.Add(new LogEntry { Level = LogLevel.Info, Message = message, Category = category }));

            mock.Setup(x => x.LogWarning(It.IsAny<string>(), It.IsAny<string>()))
                .Callback((string message, string category) =>
                    logEntries.Add(new LogEntry { Level = LogLevel.Warning, Message = message, Category = category }));

            mock.Setup(x => x.LogError(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<Exception>()))
                .Callback((string message, string category, Exception ex) =>
                    logEntries.Add(new LogEntry { Level = LogLevel.Error, Message = message, Category = category, Exception = ex }));
            
            // Add property to access log entries for verification (method doesn't exist, comment out)
            // mock.Setup(x => x.GetLogEntries()).Returns(logEntries);
            
            return mock;
        }

        /// <summary>
        /// Creates a mock UE4SSDetector with configurable detection results
        /// </summary>
        public static Mock<UE4SSDetector> CreateMockUE4SSDetector(
            UE4SSInstallStatus status = UE4SSInstallStatus.FullyInstalled,
            bool palSchemaInstalled = true)
        {
            var mock = new Mock<UE4SSDetector>("test-pal-root", Mock.Of<CacheManager>());

            mock.Setup(x => x.DetectUE4SSAsync())
                .ReturnsAsync(new UE4SSStatus
                {
                    Status = status,
                    CoreModsPresent = UE4SSDetector.RequiredCoreMods.Length,
                    UserMods = new List<string> { "TestMod1", "TestMod2" }
                });

            mock.Setup(x => x.DetectPalSchemaAsync())
                .ReturnsAsync(new PalSchemaStatus
                {
                    IsInstalled = palSchemaInstalled,
                    Mods = new List<PalSchemaMod> { new PalSchemaMod { Name = "TestPalSchemaMod" } }
                });

            mock.Setup(x => x.GetUE4SSPath())
                .Returns("test-ue4ss-path");

            mock.Setup(x => x.GetModsPath())
                .Returns("test-mods-path");

            return mock;
        }

        /// <summary>
        /// Creates a mock EnhancedInstallationEngine with configurable behavior
        /// </summary>
        public static Mock<EnhancedInstallationEngine> CreateMockInstallationEngine()
        {
            var mock = new Mock<EnhancedInstallationEngine>(
                "test-pal-root",
                Mock.Of<UE4SSDetector>(),
                Mock.Of<CacheManager>(),
                Mock.Of<EnhancedLogger>());
            
            var installations = new List<InstallationRecord>();
            
            mock.Setup(x => x.InstallModAsync(It.IsAny<string>(), It.IsAny<InstallationOptions>(), It.IsAny<IProgress<InstallationProgress>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync((string archivePath, InstallationOptions options, IProgress<InstallationProgress> progress, CancellationToken token) =>
                {
                    var operation = new InstallationOperation
                    {
                        Id = Guid.NewGuid().ToString(),
                        ArchivePath = archivePath,
                        Status = InstallationStatus.Completed
                    };

                    var record = new InstallationRecord
                    {
                        Id = operation.Id,
                        ModName = Path.GetFileNameWithoutExtension(archivePath),
                        ArchivePath = archivePath,
                        InstallDate = DateTime.UtcNow,
                        CanRollback = true,
                        InstalledFiles = new List<string> { "test-file.txt" }
                    };
                    installations.Add(record);

                    // Simulate progress reporting
                    progress?.Report(new InstallationProgress
                    {
                        OperationId = operation.Id,
                        CurrentFile = "test-file.txt",
                        ProcessedFiles = 1,
                        TotalFiles = 1,
                        ProcessedBytes = 1024,
                        TotalBytes = 1024,
                        Status = InstallationStatus.Completed
                    });

                    return new InstallationResult
                    {
                        Success = true,
                        Operation = operation,
                        InstalledFiles = new List<string> { "test-file.txt" }
                    };
                });
            
            mock.Setup(x => x.RollbackInstallationAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync((string installationId, CancellationToken token) =>
                {
                    var installation = installations.Find(i => i.Id == installationId);
                    if (installation != null)
                    {
                        installations.Remove(installation);
                        return new RollbackResult { Success = true };
                    }
                    return new RollbackResult { Success = false, ErrorMessage = "Installation not found" };
                });
            
            mock.Setup(x => x.GetAllInstallationsAsync())
                .ReturnsAsync(installations);
            
            return mock;
        }

        /// <summary>
        /// Creates a mock ModManagerService with configurable behavior
        /// </summary>
        public static Mock<ModManagerService> CreateMockModManagerService()
        {
            var mock = new Mock<ModManagerService>(
                "test-pal-root",
                Mock.Of<EnhancedInstallationEngine>(),
                Mock.Of<EnhancedLogger>());

            var mods = new List<ModItem>
            {
                new ModItem
                {
                    Id = "test-mod-1",
                    Name = "Test Mod 1",
                    Version = "1.0.0",
                    ModType = ModType.UE4SSMod,
                    IsEnabled = true,
                    LoadOrder = 1
                },
                new ModItem
                {
                    Id = "test-mod-2",
                    Name = "Test Mod 2",
                    Version = "2.0.0",
                    ModType = ModType.PalSchemaMod,
                    IsEnabled = false,
                    LoadOrder = 2
                }
            };

            mock.Setup(x => x.LoadModsAsync())
                .Returns(Task.CompletedTask);

            mock.SetupGet(x => x.Mods)
                .Returns(mods);

            mock.Setup(x => x.SetModEnabledAsync(It.IsAny<string>(), true))
                .ReturnsAsync((string modId, bool enabled) =>
                {
                    var mod = mods.Find(m => m.Id == modId);
                    if (mod != null)
                    {
                        mod.IsEnabled = enabled;
                        return true;
                    }
                    return false;
                });

            mock.Setup(x => x.SetModEnabledAsync(It.IsAny<string>(), false))
                .ReturnsAsync((string modId, bool enabled) =>
                {
                    var mod = mods.Find(m => m.Id == modId);
                    if (mod != null)
                    {
                        mod.IsEnabled = enabled;
                        return true;
                    }
                    return false;
                });

            return mock;
        }

        /// <summary>
        /// Creates test mod data for various scenarios
        /// </summary>
        public static class TestData
        {
            public static ModItem CreateTestMod(string id, string name, ModType type = ModType.UE4SSMod, bool isEnabled = true)
            {
                return new ModItem
                {
                    Id = id,
                    Name = name,
                    Version = "1.0.0",
                    ModType = type,
                    IsEnabled = isEnabled,
                    LoadOrder = 1,
                    Description = $"Test mod: {name}",
                    Author = "Test Author",
                    InstallDate = DateTime.UtcNow,
                    Size = 1024 * 1024, // 1MB
                    Dependencies = new List<ModDependency>()
                };
            }

            public static InstallationRecord CreateTestInstallation(string modName)
            {
                return new InstallationRecord
                {
                    Id = Guid.NewGuid().ToString(),
                    ModName = modName,
                    ArchivePath = $"test-{modName}.zip",
                    InstallDate = DateTime.UtcNow,
                    CanRollback = true,
                    InstalledFiles = new List<string> { $"{modName}/main.lua", $"{modName}/enabled.txt" },
                    BackupPath = $"backup-{modName}",
                    Metadata = new Dictionary<string, object>
                    {
                        ["TestProperty"] = "TestValue"
                    }
                };
            }

            public static ModProfile CreateTestProfile(string name, params string[] modIds)
            {
                var profile = new ModProfile
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = name,
                    Description = $"Test profile: {name}",
                    Created = DateTime.UtcNow
                };

                // Add mod entries
                foreach (var modId in modIds)
                {
                    profile.Mods.Add(new ModProfileEntry
                    {
                        ModId = modId,
                        IsEnabled = true,
                        LoadOrder = 0
                    });
                }

                return profile;
            }
        }
    }

    /// <summary>
    /// Log entry for testing logger behavior
    /// </summary>
    public class LogEntry
    {
        public LogLevel Level { get; set; }
        public string Message { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public Exception? Exception { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    public enum LogLevel
    {
        Info,
        Warning,
        Error
    }
}
