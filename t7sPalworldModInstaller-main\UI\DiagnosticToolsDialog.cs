using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using ModInstallerApp.Models;
using ModInstallerApp.Services;

namespace ModInstallerApp.UI
{
    /// <summary>
    /// Dialog for diagnostic tools, issue detection, and automated fixes
    /// </summary>
    public partial class DiagnosticToolsDialog : Form
    {
        private readonly DiagnosticService _diagnosticService;
        private readonly CommonIssueDetectionService _issueDetectionService;
        private readonly EnhancedLogger _logger;
        private ModInstallerApp.Models.DiagnosticReport? _currentReport;

        // UI Controls
        private TabControl tabControl;
        private TabPage diagnosticTab;
        private TabPage issuesTab;
        private TabPage reportsTab;

        // Diagnostic Tab Controls
        private Button generateReportButton;
        private ProgressBar diagnosticProgressBar;
        private RichTextBox diagnosticResultsTextBox;
        private Button saveReportButton;
        private Button exportReportButton;

        // Issues Tab Controls
        private ListView issuesListView;
        private Button detectIssuesButton;
        private Button autoFixSelectedButton;
        private Button autoFixAllButton;
        private ProgressBar issuesProgressBar;
        private Label issuesStatusLabel;

        // Reports Tab Controls
        private ListView reportsListView;
        private Button viewReportButton;
        private Button deleteReportButton;
        private RichTextBox reportPreviewTextBox;

        public DiagnosticToolsDialog(DiagnosticService diagnosticService, 
            CommonIssueDetectionService issueDetectionService, EnhancedLogger logger)
        {
            _diagnosticService = diagnosticService ?? throw new ArgumentNullException(nameof(diagnosticService));
            _issueDetectionService = issueDetectionService ?? throw new ArgumentNullException(nameof(issueDetectionService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            InitializeComponent();
            LoadExistingReports();
        }

        private void InitializeComponent()
        {
            Text = "Diagnostic Tools";
            Size = new Size(900, 700);
            StartPosition = FormStartPosition.CenterParent;
            FormBorderStyle = FormBorderStyle.Sizable;
            MinimumSize = new Size(800, 600);

            // Create tab control
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 9F)
            };

            // Create tabs
            CreateDiagnosticTab();
            CreateIssuesTab();
            CreateReportsTab();

            Controls.Add(tabControl);

            // Create bottom panel with buttons
            var bottomPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Bottom,
                BackColor = SystemColors.Control
            };

            var closeButton = new Button
            {
                Text = "Close",
                Size = new Size(80, 30),
                Anchor = AnchorStyles.Bottom | AnchorStyles.Right,
                Location = new Point(bottomPanel.Width - 90, 10),
                DialogResult = DialogResult.Cancel
            };
            closeButton.Click += (s, e) => Close();

            bottomPanel.Controls.Add(closeButton);
            Controls.Add(bottomPanel);

            CancelButton = closeButton;
        }

        private void CreateDiagnosticTab()
        {
            diagnosticTab = new TabPage("System Diagnostic");
            tabControl.TabPages.Add(diagnosticTab);

            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // Header
            var headerLabel = new Label
            {
                Text = "Generate a comprehensive diagnostic report for troubleshooting",
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Location = new Point(10, 10),
                Size = new Size(panel.Width - 20, 25),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            // Generate button
            generateReportButton = new Button
            {
                Text = "Generate Diagnostic Report",
                Size = new Size(200, 35),
                Location = new Point(10, 45),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            generateReportButton.Click += GenerateReport_Click;

            // Progress bar
            diagnosticProgressBar = new ProgressBar
            {
                Location = new Point(220, 50),
                Size = new Size(200, 25),
                Visible = false
            };

            // Results text box
            diagnosticResultsTextBox = new RichTextBox
            {
                Location = new Point(10, 90),
                Size = new Size(panel.Width - 20, panel.Height - 140),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                Font = new Font("Consolas", 9F),
                ReadOnly = true,
                BackColor = Color.White
            };

            // Action buttons
            var buttonPanel = new Panel
            {
                Height = 40,
                Dock = DockStyle.Bottom
            };

            saveReportButton = new Button
            {
                Text = "Save Report",
                Size = new Size(100, 30),
                Location = new Point(10, 5),
                Enabled = false
            };
            saveReportButton.Click += SaveReport_Click;

            exportReportButton = new Button
            {
                Text = "Export Report",
                Size = new Size(100, 30),
                Location = new Point(120, 5),
                Enabled = false
            };
            exportReportButton.Click += ExportReport_Click;

            buttonPanel.Controls.AddRange(new Control[] { saveReportButton, exportReportButton });

            panel.Controls.AddRange(new Control[] { 
                headerLabel, generateReportButton, diagnosticProgressBar, diagnosticResultsTextBox, buttonPanel 
            });
            diagnosticTab.Controls.Add(panel);
        }

        private void CreateIssuesTab()
        {
            issuesTab = new TabPage("Issue Detection");
            tabControl.TabPages.Add(issuesTab);

            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // Header
            var headerLabel = new Label
            {
                Text = "Detect and automatically fix common installation issues",
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Location = new Point(10, 10),
                Size = new Size(panel.Width - 20, 25),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            // Detect button
            detectIssuesButton = new Button
            {
                Text = "Detect Issues",
                Size = new Size(120, 35),
                Location = new Point(10, 45),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            detectIssuesButton.Click += DetectIssues_Click;

            // Progress bar
            issuesProgressBar = new ProgressBar
            {
                Location = new Point(140, 50),
                Size = new Size(200, 25),
                Visible = false
            };

            // Status label
            issuesStatusLabel = new Label
            {
                Location = new Point(350, 55),
                Size = new Size(300, 20),
                Text = "",
                ForeColor = Color.Blue
            };

            // Issues list
            issuesListView = new ListView
            {
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                CheckBoxes = true,
                Location = new Point(10, 90),
                Size = new Size(panel.Width - 20, panel.Height - 180),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right
            };

            issuesListView.Columns.AddRange(new[]
            {
                new ColumnHeader { Text = "Issue", Width = 250 },
                new ColumnHeader { Text = "Severity", Width = 80 },
                new ColumnHeader { Text = "Category", Width = 120 },
                new ColumnHeader { Text = "Auto-Fix", Width = 80 },
                new ColumnHeader { Text = "Description", Width = 300 }
            });

            issuesListView.SelectedIndexChanged += IssuesList_SelectedIndexChanged;

            // Action buttons
            var buttonPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Bottom
            };

            autoFixSelectedButton = new Button
            {
                Text = "Fix Selected Issues",
                Size = new Size(130, 30),
                Location = new Point(10, 10),
                Enabled = false
            };
            autoFixSelectedButton.Click += AutoFixSelected_Click;

            autoFixAllButton = new Button
            {
                Text = "Fix All Auto-Fixable",
                Size = new Size(130, 30),
                Location = new Point(150, 10),
                Enabled = false
            };
            autoFixAllButton.Click += AutoFixAll_Click;

            var refreshButton = new Button
            {
                Text = "Refresh",
                Size = new Size(80, 30),
                Location = new Point(290, 10)
            };
            refreshButton.Click += (s, e) => DetectIssues_Click(s, e);

            buttonPanel.Controls.AddRange(new Control[] { 
                autoFixSelectedButton, autoFixAllButton, refreshButton 
            });

            panel.Controls.AddRange(new Control[] { 
                headerLabel, detectIssuesButton, issuesProgressBar, issuesStatusLabel, 
                issuesListView, buttonPanel 
            });
            issuesTab.Controls.Add(panel);
        }

        private void CreateReportsTab()
        {
            reportsTab = new TabPage("Saved Reports");
            tabControl.TabPages.Add(reportsTab);

            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // Header
            var headerLabel = new Label
            {
                Text = "View and manage saved diagnostic reports",
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                Location = new Point(10, 10),
                Size = new Size(panel.Width - 20, 25),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            // Reports list
            reportsListView = new ListView
            {
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                Location = new Point(10, 45),
                Size = new Size(panel.Width - 20, 200),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            reportsListView.Columns.AddRange(new[]
            {
                new ColumnHeader { Text = "Generated", Width = 150 },
                new ColumnHeader { Text = "Issues Found", Width = 100 },
                new ColumnHeader { Text = "System", Width = 120 },
                new ColumnHeader { Text = "Game Version", Width = 120 },
                new ColumnHeader { Text = "File Name", Width = 200 }
            });

            reportsListView.SelectedIndexChanged += ReportsList_SelectedIndexChanged;

            // Preview
            var previewLabel = new Label
            {
                Text = "Report Preview:",
                Location = new Point(10, 255),
                Size = new Size(100, 20),
                Anchor = AnchorStyles.Left
            };

            reportPreviewTextBox = new RichTextBox
            {
                Location = new Point(10, 280),
                Size = new Size(panel.Width - 20, panel.Height - 330),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                Font = new Font("Consolas", 9F),
                ReadOnly = true,
                BackColor = Color.White
            };

            // Action buttons
            var buttonPanel = new Panel
            {
                Height = 40,
                Dock = DockStyle.Bottom
            };

            viewReportButton = new Button
            {
                Text = "View Full Report",
                Size = new Size(120, 30),
                Location = new Point(10, 5),
                Enabled = false
            };
            viewReportButton.Click += ViewReport_Click;

            deleteReportButton = new Button
            {
                Text = "Delete Report",
                Size = new Size(100, 30),
                Location = new Point(140, 5),
                Enabled = false
            };
            deleteReportButton.Click += DeleteReport_Click;

            var refreshReportsButton = new Button
            {
                Text = "Refresh",
                Size = new Size(80, 30),
                Location = new Point(250, 5)
            };
            refreshReportsButton.Click += (s, e) => LoadExistingReports();

            buttonPanel.Controls.AddRange(new Control[] { 
                viewReportButton, deleteReportButton, refreshReportsButton 
            });

            panel.Controls.AddRange(new Control[] { 
                headerLabel, reportsListView, previewLabel, reportPreviewTextBox, buttonPanel 
            });
            reportsTab.Controls.Add(panel);
        }

        private async void GenerateReport_Click(object sender, EventArgs e)
        {
            try
            {
                generateReportButton.Enabled = false;
                diagnosticProgressBar.Visible = true;
                diagnosticProgressBar.Style = ProgressBarStyle.Marquee;
                diagnosticResultsTextBox.Text = "Generating diagnostic report...\n";

                _currentReport = await _diagnosticService.GenerateReportAsync();

                // Display report summary
                var summary = FormatReportSummary(_currentReport);
                diagnosticResultsTextBox.Text = summary;

                saveReportButton.Enabled = true;
                exportReportButton.Enabled = true;

                LoadExistingReports(); // Refresh reports list
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to generate diagnostic report", "UI", ex);
                diagnosticResultsTextBox.Text = $"Error generating report: {ex.Message}";
                MessageBox.Show($"Failed to generate diagnostic report: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                generateReportButton.Enabled = true;
                diagnosticProgressBar.Visible = false;
            }
        }

        private async void DetectIssues_Click(object sender, EventArgs e)
        {
            try
            {
                detectIssuesButton.Enabled = false;
                issuesProgressBar.Visible = true;
                issuesProgressBar.Style = ProgressBarStyle.Marquee;
                issuesStatusLabel.Text = "Detecting issues...";
                issuesStatusLabel.ForeColor = Color.Blue;

                var issues = await _issueDetectionService.DetectAllIssuesAsync();

                // Populate issues list
                issuesListView.Items.Clear();
                foreach (var issue in issues)
                {
                    var item = new ListViewItem(new[]
                    {
                        issue.Title,
                        issue.Severity.ToString(),
                        issue.Category,
                        issue.CanAutoFix ? "Yes" : "No",
                        issue.Description
                    })
                    {
                        Tag = issue,
                        BackColor = GetSeverityColor(issue.Severity)
                    };

                    issuesListView.Items.Add(item);
                }

                issuesStatusLabel.Text = $"Found {issues.Count} issues";
                issuesStatusLabel.ForeColor = issues.Any() ? Color.Red : Color.Green;

                autoFixAllButton.Enabled = issues.Any(i => i.CanAutoFix);
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to detect issues", "UI", ex);
                issuesStatusLabel.Text = $"Error: {ex.Message}";
                issuesStatusLabel.ForeColor = Color.Red;
                MessageBox.Show($"Failed to detect issues: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                detectIssuesButton.Enabled = true;
                issuesProgressBar.Visible = false;
            }
        }

        private async void AutoFixSelected_Click(object sender, EventArgs e)
        {
            var selectedIssues = issuesListView.CheckedItems.Cast<ListViewItem>()
                .Select(item => (DiagnosticIssue)item.Tag)
                .Where(issue => issue.CanAutoFix)
                .ToList();

            if (!selectedIssues.Any())
            {
                MessageBox.Show("No auto-fixable issues selected.", "No Issues Selected", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            await FixIssues(selectedIssues);
        }

        private async void AutoFixAll_Click(object sender, EventArgs e)
        {
            var autoFixableIssues = issuesListView.Items.Cast<ListViewItem>()
                .Select(item => (DiagnosticIssue)item.Tag)
                .Where(issue => issue.CanAutoFix)
                .ToList();

            if (!autoFixableIssues.Any())
            {
                MessageBox.Show("No auto-fixable issues found.", "No Issues", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var result = MessageBox.Show($"This will attempt to automatically fix {autoFixableIssues.Count} issues. Continue?", 
                "Confirm Auto-Fix", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                await FixIssues(autoFixableIssues);
            }
        }

        private async Task FixIssues(List<DiagnosticIssue> issues)
        {
            try
            {
                issuesProgressBar.Visible = true;
                issuesProgressBar.Style = ProgressBarStyle.Continuous;
                issuesProgressBar.Maximum = issues.Count;
                issuesProgressBar.Value = 0;

                var fixedCount = 0;
                var failedCount = 0;

                foreach (var issue in issues)
                {
                    issuesStatusLabel.Text = $"Fixing: {issue.Title}";
                    
                    var success = await _issueDetectionService.AutoFixIssueAsync(issue.Id);
                    if (success)
                    {
                        fixedCount++;
                        // Remove fixed issue from list
                        var listItem = issuesListView.Items.Cast<ListViewItem>()
                            .FirstOrDefault(item => ((DiagnosticIssue)item.Tag).Id == issue.Id);
                        if (listItem != null)
                        {
                            issuesListView.Items.Remove(listItem);
                        }
                    }
                    else
                    {
                        failedCount++;
                    }

                    issuesProgressBar.Value++;
                }

                issuesStatusLabel.Text = $"Fixed: {fixedCount}, Failed: {failedCount}";
                issuesStatusLabel.ForeColor = failedCount == 0 ? Color.Green : Color.Orange;

                MessageBox.Show($"Auto-fix completed!\n\nFixed: {fixedCount}\nFailed: {failedCount}", 
                    "Auto-Fix Results", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to auto-fix issues", "UI", ex);
                MessageBox.Show($"Error during auto-fix: {ex.Message}", "Auto-Fix Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                issuesProgressBar.Visible = false;
            }
        }

        private void IssuesList_SelectedIndexChanged(object sender, EventArgs e)
        {
            autoFixSelectedButton.Enabled = issuesListView.CheckedItems.Count > 0 &&
                issuesListView.CheckedItems.Cast<ListViewItem>()
                    .Any(item => ((DiagnosticIssue)item.Tag).CanAutoFix);
        }

        private void ReportsList_SelectedIndexChanged(object sender, EventArgs e)
        {
            var hasSelection = reportsListView.SelectedItems.Count > 0;
            viewReportButton.Enabled = hasSelection;
            deleteReportButton.Enabled = hasSelection;

            if (hasSelection)
            {
                var reportPath = (string)reportsListView.SelectedItems[0].Tag;
                LoadReportPreview(reportPath);
            }
            else
            {
                reportPreviewTextBox.Text = "";
            }
        }

        private void SaveReport_Click(object sender, EventArgs e)
        {
            if (_currentReport == null) return;

            using var dialog = new SaveFileDialog
            {
                Title = "Save Diagnostic Report",
                Filter = "JSON Files (*.json)|*.json|Text Files (*.txt)|*.txt|All Files (*.*)|*.*",
                DefaultExt = "json",
                FileName = $"diagnostic_report_{DateTime.Now:yyyyMMdd_HHmmss}.json"
            };

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    var json = System.Text.Json.JsonSerializer.Serialize(_currentReport, 
                        new System.Text.Json.JsonSerializerOptions { WriteIndented = true });
                    File.WriteAllText(dialog.FileName, json);
                    
                    MessageBox.Show("Report saved successfully!", "Save Complete", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    _logger.LogError("Failed to save report", "UI", ex);
                    MessageBox.Show($"Failed to save report: {ex.Message}", "Save Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void ExportReport_Click(object sender, EventArgs e)
        {
            if (_currentReport == null) return;

            using var dialog = new SaveFileDialog
            {
                Title = "Export Diagnostic Report",
                Filter = "HTML Files (*.html)|*.html|Text Files (*.txt)|*.txt|All Files (*.*)|*.*",
                DefaultExt = "html",
                FileName = $"diagnostic_report_{DateTime.Now:yyyyMMdd_HHmmss}.html"
            };

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    var html = GenerateHtmlReport(_currentReport);
                    File.WriteAllText(dialog.FileName, html);
                    
                    MessageBox.Show("Report exported successfully!", "Export Complete", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    _logger.LogError("Failed to export report", "UI", ex);
                    MessageBox.Show($"Failed to export report: {ex.Message}", "Export Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void ViewReport_Click(object sender, EventArgs e)
        {
            if (reportsListView.SelectedItems.Count > 0)
            {
                var reportPath = (string)reportsListView.SelectedItems[0].Tag;
                try
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = reportPath,
                        UseShellExecute = true
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogError("Failed to open report", "UI", ex);
                    MessageBox.Show($"Failed to open report: {ex.Message}", "Open Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void DeleteReport_Click(object sender, EventArgs e)
        {
            if (reportsListView.SelectedItems.Count > 0)
            {
                var reportPath = (string)reportsListView.SelectedItems[0].Tag;
                var fileName = Path.GetFileName(reportPath);
                
                var result = MessageBox.Show($"Are you sure you want to delete the report '{fileName}'?", 
                    "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    try
                    {
                        File.Delete(reportPath);
                        LoadExistingReports();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("Failed to delete report", "UI", ex);
                        MessageBox.Show($"Failed to delete report: {ex.Message}", "Delete Error", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void LoadExistingReports()
        {
            // Implementation to load existing diagnostic reports
            reportsListView.Items.Clear();
            // This would scan the diagnostics directory for saved reports
        }

        private void LoadReportPreview(string reportPath)
        {
            try
            {
                var content = File.ReadAllText(reportPath);
                if (content.Length > 5000)
                {
                    content = content.Substring(0, 5000) + "\n\n... (truncated)";
                }
                reportPreviewTextBox.Text = content;
            }
            catch (Exception ex)
            {
                reportPreviewTextBox.Text = $"Error loading preview: {ex.Message}";
            }
        }

        private string FormatReportSummary(ModInstallerApp.Models.DiagnosticReport report)
        {
            var summary = $"DIAGNOSTIC REPORT SUMMARY\n";
            summary += $"Generated: {report.GeneratedAt:yyyy-MM-dd HH:mm:ss}\n";
            summary += $"Generated By: {report.GeneratedBy}\n\n";

            summary += $"SYSTEM INFORMATION\n";
            summary += $"OS: {report.SystemInfo.OperatingSystem}\n";
            summary += $"Architecture: {report.SystemInfo.ProcessorArchitecture}\n";
            summary += $"Memory: {FormatBytes(report.SystemInfo.TotalMemory)} total, {FormatBytes(report.SystemInfo.AvailableMemory)} available\n";
            summary += $"Administrator: {(report.SystemInfo.IsAdministrator ? "Yes" : "No")}\n\n";

            summary += $"GAME INFORMATION\n";
            summary += $"Game Path: {report.GameInfo.GamePath}\n";
            summary += $"Game Version: {report.GameInfo.GameVersion}\n";
            summary += $"Game Running: {(report.GameInfo.IsGameRunning ? "Yes" : "No")}\n";
            summary += $"UE4SS Status: {report.GameInfo.UE4SSStatus.Status}\n";
            summary += $"Installed Mods: {report.GameInfo.InstalledMods.Count}\n";
            summary += $"Enabled Mods: {report.GameInfo.EnabledMods.Count}\n\n";

            summary += $"DETECTED ISSUES ({report.DetectedIssues.Count})\n";
            foreach (var issue in report.DetectedIssues.Take(10))
            {
                summary += $"• [{issue.Severity}] {issue.Title}\n";
            }
            if (report.DetectedIssues.Count > 10)
            {
                summary += $"... and {report.DetectedIssues.Count - 10} more issues\n";
            }

            summary += $"\nRECOMMENDED ACTIONS\n";
            foreach (var action in report.RecommendedActions.Take(5))
            {
                summary += $"• {action}\n";
            }

            return summary;
        }

        private string GenerateHtmlReport(ModInstallerApp.Models.DiagnosticReport report)
        {
            // Implementation to generate HTML report
            return $"<html><body><h1>Diagnostic Report</h1><p>Generated: {report.GeneratedAt}</p></body></html>";
        }

        private Color GetSeverityColor(DiagnosticSeverity severity)
        {
            return severity switch
            {
                DiagnosticSeverity.Critical => Color.DarkRed,
                DiagnosticSeverity.Error => Color.LightCoral,
                DiagnosticSeverity.Warning => Color.LightYellow,
                DiagnosticSeverity.Info => Color.LightBlue,
                _ => Color.White
            };
        }

        private string FormatBytes(long bytes)
        {
            if (bytes == 0) return "0 B";
            
            string[] sizes = { "B", "KB", "MB", "GB" };
            int order = 0;
            double size = bytes;
            
            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }
            
            return $"{size:0.##} {sizes[order]}";
        }
    }
}
